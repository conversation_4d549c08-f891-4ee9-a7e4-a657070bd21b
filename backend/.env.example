# GameFlex Backend Environment Variables
# This file contains secrets that will be automatically uploaded to AWS Secrets Manager
# Copy this file to .env and modify with your actual credentials

# AWS Administrator Credentials - FOR USER SETUP ONLY
# These credentials are used ONLY to create GitLab CI users and attach policies
# Individual users will handle their own CDK bootstrapping
AWS_ADMIN_ACCESS_KEY_ID=
AWS_ADMIN_SECRET_ACCESS_KEY=
AWS_ACCOUNT_ID=************
AWS_DEFAULT_REGION=us-west-2

# AWS Deployment Credentials by Environment
# Development Environment
AWS_ACCESS_KEY_ID_DEV=
AWS_SECRET_ACCESS_KEY_DEV=
AWS_REGION_DEV=us-west-2

# Staging Environment
AWS_ACCESS_KEY_ID_STAGING=
AWS_SECRET_ACCESS_KEY_STAGING=
AWS_REGION_STAGING=us-west-2

# Production Environment
AWS_ACCESS_KEY_ID_PROD=
AWS_SECRET_ACCESS_KEY_PROD=
AWS_REGION_PROD=us-east-1

# CloudFlare R2 Configuration - DE<PERSON><PERSON>OPMENT
# These will be stored in AWS Secrets Manager
R2_ACCOUNT_ID=79ee497f37a6902472563e9f3fe8f451
R2_ACCESS_KEY_ID=ac20ad141b9a1ca9d2470e9b3c663320
R2_SECRET_ACCESS_KEY=62d049b50e3df9701f70b7897e336d87d03a50c4c1c370b8cb28c7f54c37e451
R2_ENDPOINT=https://79ee497f37a6902472563e9f3fe8f451.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-development
R2_PUBLIC_URL=https://pub-34709f09e8384ef1a67928492571c01d.r2.dev

# Application Configuration - DEVELOPMENT
# These will be stored in AWS Secrets Manager
CLOUDFLARE_API_TOKEN=****************************************
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=DevTest123!
DEBUG_MODE=development
ENVIRONMENT=development

# API Configuration - DEVELOPMENT
API_URL_REMOTE=https://bllxxn79s2.execute-api.us-west-2.amazonaws.com/v1
API_BASE_URL=https://bllxxn79s2.execute-api.us-west-2.amazonaws.com/v1

# AWS Resources - STAGING (for tests)
USER_POOL_ID=us-west-2_Eoj9I6lEI
USER_POOL_CLIENT_ID=1muni71i0vo5opkfstefafo8vk
USERS_TABLE=gameflex-staging-Users
POSTS_TABLE=gameflex-staging-Posts
MEDIA_TABLE=gameflex-staging-Media
USER_PROFILES_TABLE=gameflex-staging-UserProfiles
COMMENTS_TABLE=gameflex-staging-Comments
LIKES_TABLE=gameflex-staging-Likes
FOLLOWS_TABLE=gameflex-staging-Follows
CHANNELS_TABLE=gameflex-staging-Channels
CHANNEL_MEMBERS_TABLE=gameflex-staging-ChannelMembers
REFLEXES_TABLE=gameflex-staging-Reflexes

# Sign in with Apple Configuration
# These will be stored in AWS Secrets Manager
APPLE_TEAM_ID=YOUR_APPLE_TEAM_ID
APPLE_CLIENT_ID=YOUR_APPLE_CLIENT_ID
APPLE_KEY_ID=YOUR_APPLE_KEY_ID
APPLE_PRIVATE_KEY=YOUR_APPLE_PRIVATE_KEY_CONTENT
