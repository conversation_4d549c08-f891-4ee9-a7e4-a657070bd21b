# GameFlex Backend GitLab CI/CD Pipeline
# Backend-focused pipeline with SAST, Secrets Detection, and multi-environment deployment
#
# SETUP: Run ./setup/bootstrap.sh to automatically create users and configure environments
# For detailed setup instructions, see setup/SETUP.md

# Global variables
variables:
  # Docker images
  NODE_IMAGE: "node:18-alpine"
  AWS_CLI_IMAGE: "amazon/aws-cli:latest"

  # Node.js configuration
  NODE_VERSION: "18"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"

  # AWS configuration
  AWS_DEFAULT_REGION: "us-west-2"

  # Project configuration
  PROJECT_NAME: "gameflex"

  # Security scanning
  SAST_EXCLUDED_PATHS: "node_modules,build,dist,cdk.out,coverage"
  SAST_DEFAULT_ANALYZERS: "eslint,nodejs-scan,semgrep"
  DS_DEFAULT_ANALYZERS: "gemnasium"

# Pipeline stages
stages:
  - prepare
  - security
  - test
  - synth
  - build
  - lint-cloudformation
  - deploy-dev
  - deploy-staging
  - deploy-production

# Global cache configuration
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .npm/

# Include GitLab security templates
include:
  - template: SAST.gitlab-ci.yml
  - template: Secret-Detection.gitlab-ci.yml
  - template: Dependency-Scanning.gitlab-ci.yml

# ============================================================================
# PREPARATION STAGE
# ============================================================================

# Install backend dependencies
backend-deps:
  stage: prepare
  image: $NODE_IMAGE
  script:
    - npm ci --cache .npm --prefer-offline
  cache:
    key: backend-deps-$CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
      - .npm/
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - "**/*"
      - .gitlab-ci.yml

# ============================================================================
# SECURITY STAGE
# ============================================================================

# SAST is automatically included from the template - no custom job needed

# Security scanning jobs are automatically included from templates



# ============================================================================
# TESTING STAGE - LOCAL TESTING ONLY
# ============================================================================

# Note: Backend testing removed from pipeline as server won't reflect current changes
# Run tests locally using: npm run test or npm run test:coverage

# ============================================================================
# CDK SYNTHESIS STAGE
# ============================================================================

# Synthesize CDK stack to CloudFormation templates
cdk-synth:
  stage: synth
  image: $NODE_IMAGE
  dependencies:
    - backend-deps
  script:
    - echo "Synthesizing CDK stack to CloudFormation templates..."
    - npm run synth
  artifacts:
    paths:
      - cdk.out/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"

# ============================================================================
# BUILD STAGE
# ============================================================================

# Build Lambda functions
lambda-build:
  stage: build
  image: $NODE_IMAGE
  dependencies:
    - backend-deps
  script:
    - echo "Building TypeScript Lambda functions..."
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"

# ============================================================================
# CLOUDFORMATION LINTING STAGE
# ============================================================================

# CDK Nag - Check CDK applications for best practices
cdk-nag:
  stage: lint-cloudformation
  image: $NODE_IMAGE
  dependencies:
    - cdk-synth
  before_script:
    - npm install -g cdk-nag-cli
  script:
    - echo "Running CDK Nag checks..."
    - cdk-nag --app "cdk.out" --output-format json --output-file cdk-nag-report.json || true
    - echo "CDK Nag analysis complete"
  artifacts:
    reports:
      junit: cdk-nag-report.json
    paths:
      - cdk-nag-report.json
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"
  allow_failure: true

# CloudFormation Linter (cfn-lint)
cfn-lint:
  stage: lint-cloudformation
  image: python:3.9-alpine
  dependencies:
    - cdk-synth
  before_script:
    - pip install cfn-lint
  script:
    - echo "Running CloudFormation Linter..."
    - find cdk.out -name "*.template.json" -exec cfn-lint {} \; || true
    - echo "CloudFormation linting complete"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"
  allow_failure: true

# cfn_nag - Security analysis for CloudFormation templates
cfn-nag:
  stage: lint-cloudformation
  image: ruby:3.1-alpine
  dependencies:
    - cdk-synth
  before_script:
    - apk add --no-cache build-base
    - gem install cfn-nag
  script:
    - echo "Running cfn_nag security analysis..."
    - find cdk.out -name "*.template.json" -exec cfn_nag_scan --input-path {} \; || true
    - echo "cfn_nag security analysis complete"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"
  allow_failure: true

# ============================================================================
# DEVELOPMENT DEPLOYMENT
# ============================================================================

# Deploy backend to development
deploy-backend-dev:
  stage: deploy-dev
  image: $AWS_CLI_IMAGE
  dependencies:
    - lambda-build
    - cdk-synth
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=development
    - ./deploy.sh development
  environment:
    name: development
    url: https://dev.api.gameflex.io
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEV
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEV



# ============================================================================
# STAGING DEPLOYMENT
# ============================================================================

# Deploy backend to staging
deploy-backend-staging:
  stage: deploy-staging
  image: $AWS_CLI_IMAGE
  dependencies:
    - lambda-build
    - cdk-synth
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=staging
    - ./deploy.sh staging
  environment:
    name: staging
    url: https://staging.api.gameflex.io
  rules:
    - if: $CI_COMMIT_BRANCH == "staging"
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_STAGING
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_STAGING
  when: manual



# ============================================================================
# PRODUCTION DEPLOYMENT
# ============================================================================

# Deploy backend to production
deploy-backend-production:
  stage: deploy-production
  image: $AWS_CLI_IMAGE
  dependencies:
    - lambda-build
    - cdk-synth
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=production
    - ./deploy.sh production
  environment:
    name: production
    url: https://api.gameflex.io
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_PROD
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_PROD
    AWS_DEFAULT_REGION: "us-east-1"
  when: manual
  allow_failure: false



# ============================================================================
# SECURITY GATES AND QUALITY CHECKS
# ============================================================================

# Security gate - block deployment if vulnerabilities found
security-gate:
  stage: deploy-dev
  image: alpine:latest
  script:
    - echo "Checking security scan results..."
    - |
      if [ -f gl-sast-report.json ]; then
        HIGH_VULNS=$(cat gl-sast-report.json | grep -c '"severity":"High"' || echo "0")
        CRITICAL_VULNS=$(cat gl-sast-report.json | grep -c '"severity":"Critical"' || echo "0")

        if [ "$CRITICAL_VULNS" -gt "0" ]; then
          echo "❌ CRITICAL vulnerabilities found: $CRITICAL_VULNS"
          echo "Deployment blocked due to critical security issues"
          exit 1
        fi

        if [ "$HIGH_VULNS" -gt "5" ]; then
          echo "⚠️  Too many HIGH vulnerabilities found: $HIGH_VULNS"
          echo "Please fix high-severity issues before deployment"
          exit 1
        fi

        echo "✅ Security check passed"
      else
        echo "⚠️  No SAST report found, proceeding with caution"
      fi
    - |
      if [ -f gl-secret-detection-report.json ]; then
        SECRETS=$(cat gl-secret-detection-report.json | grep -c '"category":"secret"' || echo "0")

        if [ "$SECRETS" -gt "0" ]; then
          echo "❌ Secrets detected in code: $SECRETS"
          echo "Deployment blocked due to exposed secrets"
          exit 1
        fi

        echo "✅ No secrets detected"
      else
        echo "⚠️  No secrets detection report found"
      fi
  dependencies: []
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID
  allow_failure: false

# Quality gate - basic checks only (testing done locally)
quality-gate:
  stage: deploy-dev
  image: alpine:latest
  script:
    - echo "Checking code quality metrics..."
    - echo "Note Test coverage checks are performed locally"
    - echo "Quality checks passed"
  dependencies: []
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "staging"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: false

# ============================================================================
# NOTIFICATION AND CLEANUP
# ============================================================================

# Notify on deployment success
notify-success:
  stage: deploy-production
  image: alpine:latest
  script:
    - echo "Deployment completed successfully!"
    - echo "Environment $CI_ENVIRONMENT_NAME"
    - echo "Commit $CI_COMMIT_SHA"
    - echo "Pipeline $CI_PIPELINE_URL"
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "staging"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: on_success

# Notify on deployment failure
notify_failure:
  stage: deploy-production
  image: alpine:latest
  script:
    - echo "Deployment failed!"
    - echo "Environment $CI_ENVIRONMENT_NAME"
    - echo "Commit $CI_COMMIT_SHA"
    - echo "Pipeline $CI_PIPELINE_URL"
    - echo "Please check the pipeline logs for details"
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "staging"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: on_failure
