#!/bin/bash

# GameFlex CDK Deployment Script
# This script deploys the GameFlex backend using AWS CDK

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
PROJECT_NAME="gameflex"
SKIP_CONFIRMATION=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [ENVIRONMENT]"
    echo ""
    echo "Deploy GameFlex backend using AWS CDK"
    echo ""
    echo "Arguments:"
    echo "  ENVIRONMENT    Target environment (development, staging, production) [default: development]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -y, --yes      Skip confirmation prompts"
    echo "  -v, --verbose  Enable verbose output"
    echo "  --diff         Show diff before deployment"
    echo "  --synth-only   Only synthesize, don't deploy"
    echo ""
    echo "Examples:"
    echo "  $0                    # Deploy to development"
    echo "  $0 staging            # Deploy to staging"
    echo "  $0 -y production      # Deploy to production without confirmation"
    echo "  $0 --diff staging     # Show diff for staging deployment"
}

# Parse command line arguments
SHOW_DIFF=false
SYNTH_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --diff)
            SHOW_DIFF=true
            shift
            ;;
        --synth-only)
            SYNTH_ONLY=true
            shift
            ;;
        development|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done



# Set stack name
STACK_NAME="${PROJECT_NAME}-${ENVIRONMENT}"

print_status "Starting CDK deployment for GameFlex backend"
print_status "Environment: $ENVIRONMENT"
print_status "Stack Name: $STACK_NAME"
print_status "Project: $PROJECT_NAME"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid"
    print_error "Please run 'aws configure' or set up your AWS credentials"
    exit 1
fi

# Load environment variables from .env file
ENV_FILE="$(dirname "$0")/.env"
if [[ ! -f "$ENV_FILE" ]]; then
    print_error ".env file not found at $ENV_FILE"
    exit 1
fi

# Save the command line environment before sourcing .env
SAVED_ENVIRONMENT="$ENVIRONMENT"

# Source the .env file
set -a  # automatically export all variables
source "$ENV_FILE"
set +a  # stop automatically exporting

# Restore the command line environment (don't let .env override it)
ENVIRONMENT="$SAVED_ENVIRONMENT"



# Load environment-specific AWS credentials and region
case "$ENVIRONMENT" in
    "development")
        export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_DEV"
        export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_DEV"
        export AWS_DEFAULT_REGION="$AWS_REGION_DEV"
        AWS_REGION="$AWS_REGION_DEV"
        ;;
    "staging")
        export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_STAGING"
        export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_STAGING"
        export AWS_DEFAULT_REGION="$AWS_REGION_STAGING"
        AWS_REGION="$AWS_REGION_STAGING"
        ;;
    "production")
        export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_PROD"
        export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_PROD"
        export AWS_DEFAULT_REGION="$AWS_REGION_PROD"
        AWS_REGION="$AWS_REGION_PROD"
        ;;
    *)
        print_error "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

# Get AWS account info
AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)

print_status "AWS Account: $AWS_ACCOUNT"
print_status "AWS Region: $AWS_REGION"

# Check if CDK is installed
if ! command -v cdk &> /dev/null; then
    print_error "AWS CDK is not installed"
    print_error "Please install it with: npm install -g aws-cdk"
    exit 1
fi



# Determine CDK toolkit stack name and qualifier based on environment
case "$ENVIRONMENT" in
    "development")
        CDK_TOOLKIT_STACK="CDKToolkit-GameFlex-Development"
        CDK_QUALIFIER="gfdev"
        ;;
    "staging")
        CDK_TOOLKIT_STACK="CDKToolkit-GameFlex-Staging"
        CDK_QUALIFIER="gfstaging"
        ;;
    "production")
        CDK_TOOLKIT_STACK="CDKToolkit-GameFlex-Production"
        CDK_QUALIFIER="gfprod"
        ;;
    *)
        print_error "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

# Check if CDK is bootstrapped for this environment
if ! aws cloudformation describe-stacks --stack-name $CDK_TOOLKIT_STACK --region $AWS_REGION &> /dev/null; then
    print_error "CDK is not bootstrapped for $ENVIRONMENT environment"
    print_error "Expected CDK toolkit stack: $CDK_TOOLKIT_STACK"
    print_error "Please run the bootstrap script: ./setup/bootstrap-cdk.sh"
    exit 1
fi

print_status "CDK toolkit stack: $CDK_TOOLKIT_STACK"
print_status "CDK qualifier: $CDK_QUALIFIER"

# Ensure required secrets exist
print_status "Ensuring required secrets exist..."
if ! ./scripts/ensure-secrets.sh $PROJECT_NAME $ENVIRONMENT; then
    print_error "Failed to ensure secrets exist"
    exit 1
fi

# Build the project
print_status "Building TypeScript project..."
if ! npm run build; then
    print_error "Failed to build TypeScript project"
    exit 1
fi

# Install dependencies for Lambda functions
print_status "Installing Lambda function dependencies..."
for lambda_dir in src/*/; do
    if [ -f "${lambda_dir}package.json" ]; then
        print_status "Installing dependencies for $(basename $lambda_dir)..."
        (cd "$lambda_dir" && npm install --production)
    fi
done



# Synthesize the stack
print_status "Synthesizing CDK stack..."
if $VERBOSE; then
    cdk synth $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER
else
    cdk synth $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER --quiet
fi

if [ $? -ne 0 ]; then
    print_error "Failed to synthesize CDK stack"
    exit 1
fi

# Show diff if requested
if $SHOW_DIFF; then
    print_status "Showing deployment diff..."
    cdk diff $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER
fi

# Exit if synth-only mode
if $SYNTH_ONLY; then
    print_success "Synthesis completed successfully"
    exit 0
fi

# Confirmation prompt (unless skipped)
if [ "$SKIP_CONFIRMATION" = false ]; then
    echo ""
    print_warning "You are about to deploy to the $ENVIRONMENT environment"
    print_warning "This will create/update AWS resources which may incur costs"
    echo ""
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
fi

# Deploy the stack
print_status "Deploying CDK stack..."
if $VERBOSE; then
    cdk deploy $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER --require-approval never
else
    cdk deploy $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER --require-approval never --progress events
fi

if [ $? -eq 0 ]; then
    print_success "Deployment completed successfully!"
    print_status "Stack: $STACK_NAME"
    print_status "Environment: $ENVIRONMENT"
    
    # Get stack outputs
    print_status "Retrieving stack outputs..."
    aws cloudformation describe-stacks --stack-name $STACK_NAME --region $AWS_REGION --query 'Stacks[0].Outputs' --output table
    
else
    print_error "Deployment failed"
    exit 1
fi
